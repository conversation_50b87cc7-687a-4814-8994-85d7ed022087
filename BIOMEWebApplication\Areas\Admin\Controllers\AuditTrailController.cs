using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using BIOME.Services;
using BIOME.ViewModels;
using BIOME.Models;
using BIOMEWebApplication.Areas.Admin.Constants;
using BIOMEWebApplication.Extensions;
using BIOMEWebApplication.ModelState;
using BIOME.Constants;
using System.Web.Mvc;
using Microsoft.AspNet.Identity;
using MvcPaging;
using System.Globalization;
using BIOMEWebApplication.Authorization;
using System.Reflection;

namespace BIOMEWebApplication.Areas.Admin.Controllers
{
#if INTERNET
    [AccessDeniedAuthorize(Roles = UserRoles.FullNoAccess)]
#elif INTRANET
    [AccessDeniedAuthorize(Roles = UserRoles.SystemAdmin)]
#endif
    [RoutePrefix(ControllerName.AuditTrail)]
    public class AuditTrailController : AdminControllerBase
    {
        #region Fields

        private IAuditTrailService auditTrailService;
        private IUserService userService;
        private ISystemParametersService systemParametersService;
        private readonly log4net.ILog logger = log4net.LogManager.GetLogger(typeof(AuditTrailController));

        #endregion

        #region Constructors

        public AuditTrailController(IAuditTrailService auditTrailService, ISystemParametersService systemParametersService, IPageService pageService, IUserService userService) : base(pageService, userService)
        {
            this.auditTrailService = auditTrailService;
            this.userService = userService;
            this.systemParametersService = systemParametersService;

            ViewBag.MainMenu = BIOME.Enumerations.Menu.Admin.MainMenu.AuditTrails;
        }

        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            base.Initialize(requestContext);

            BreadcrumbsList.Add(new Breadcrumb() { Name = "Audit Trail", UrlRootPath = Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Login, page = 1 }) });
        }

        #endregion

        #region Public Methods

        // GET: Admin/AuditTrailPage
        [Route("AuditTrailPage/{actionid}/{page}", Name = AuditTrailControllerRoute.GetAuditTrailPage)]
        public ActionResult AuditTrailPage(int actionid, int page, string Email = "", string PeriodFrom = "", string PeriodTo = "", string ipAddress = "")
        {
            logger.Info($"AuditTrailPage - START: actionid={actionid}, page={page}, Email='{Email}', PeriodFrom='{PeriodFrom}', PeriodTo='{PeriodTo}', ipAddress='{ipAddress}'");

            try
            {
#if INTRANET
                if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                {
                    logger.Warn($"AuditTrailPage - OTP not verified, redirecting to OTP page");
                    return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
                }
#endif
                logger.Debug($"AuditTrailPage - Getting system parameters");
                int auditLogsPeriod = systemParametersService.GetAuditLogsStoragePeriod();
                int paginationSize = systemParametersService.GetPaginationPageSize();
                logger.Debug($"AuditTrailPage - System parameters: auditLogsPeriod={auditLogsPeriod}, paginationSize={paginationSize}");

                logger.Debug($"AuditTrailPage - Processing date parameters");
                DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
                if (!string.IsNullOrEmpty(PeriodFrom))
                {
                    try
                    {
                        tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                        logger.Debug($"AuditTrailPage - Parsed PeriodFrom: {tPeriodFrom}");
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"AuditTrailPage - Error parsing PeriodFrom '{PeriodFrom}': {ex.Message}");
                        throw new ArgumentException($"Invalid PeriodFrom date format: {PeriodFrom}", ex);
                    }
                }
                else
                {
                    PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
                    logger.Debug($"AuditTrailPage - Default PeriodFrom set to: {PeriodFrom}");
                }

                DateTimeOffset tPeriodTo = DateTimeOffset.Now;
                if (!string.IsNullOrEmpty(PeriodTo))
                {
                    try
                    {
                        tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                        logger.Debug($"AuditTrailPage - Parsed PeriodTo: {tPeriodTo}");
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"AuditTrailPage - Error parsing PeriodTo '{PeriodTo}': {ex.Message}");
                        throw new ArgumentException($"Invalid PeriodTo date format: {PeriodTo}", ex);
                    }
                }
                else
                {
                    PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
                    logger.Debug($"AuditTrailPage - Default PeriodTo set to: {PeriodTo}");
                }

                logger.Debug($"AuditTrailPage - Calling GetListAuditTrail with actionid={actionid}, Email='{Email}'");
                IPagedList<AuditTrailLogging> pagedItems = auditTrailService.GetListAuditTrail(actionid, Email, tPeriodFrom, tPeriodTo, ipAddress, page - 1, paginationSize);
                logger.Info($"AuditTrailPage - Retrieved {pagedItems?.Count() ?? 0} items, TotalItemCount={pagedItems?.TotalItemCount ?? 0}");

                logger.Debug($"AuditTrailPage - Setting up view based on actionid={actionid}");
                if (actionid == (int)BIOME.Enumerations.Audit.Action.Login)
                {
                    ViewBag.Title = "Audit Trail Login";
                    ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Login;
                    ConstructBreadcrumbs(new Breadcrumb() { Name = "Login", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Login, page = 1 }) });
                    if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                        auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Login, "");
                }
                else if (actionid == (int)BIOME.Enumerations.Audit.Action.Logout)
                {
                    ViewBag.Title = "Audit Trail Logout";
                    ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Logout;
                    ConstructBreadcrumbs(new Breadcrumb() { Name = "Logout", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Logout, page = 1 }) });
                    if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                        auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Logout, "");
                }
                else if (actionid == (int)BIOME.Enumerations.Audit.Action.AccessResourcesUnsuccessful)
                {
                    ViewBag.Title = "Audit Trail Access Resources";
                    ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.AccessResourcesUnsuccessful;
                    ConstructBreadcrumbs(new Breadcrumb() { Name = "Access Resources", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.AccessResourcesUnsuccessful, page = 1 }) });
                    if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                        auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_AccessResources, "");
                }
                else if (actionid == (int)BIOME.Enumerations.Audit.Action.SystemStartUpShutDown)
                {
                    ViewBag.Title = "Audit Trail Startup/Shutdown";
                    ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.SystemStartUpShutDown;
                    ConstructBreadcrumbs(new Breadcrumb() { Name = "Startup/Shutdown", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.SystemStartUpShutDown, page = 1 }) });
                    if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                        auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Startup, "");
                }
                else if (actionid == (int)BIOME.Enumerations.Audit.Action.Transactions)
                {
                    ViewBag.Title = "Audit Trail Transactions";
                    ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Transactions;
                    ConstructBreadcrumbs(new Breadcrumb() { Name = "Transactions", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Transactions, page = 1 }) });
                    if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                        auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Transactions, "");
                }

                ViewBag.pagedItems = pagedItems;
                ViewBag.varpage = page;
                ViewBag.actionid = actionid;

                logger.Debug($"AuditTrailPage - Creating search view model");
                AuditTrailViewModel.SearchViewModel searchVM = new AuditTrailViewModel.SearchViewModel();
                searchVM.Email = Email;
                searchVM.PeriodFrom = PeriodFrom;
                searchVM.PeriodTo = PeriodTo;

                logger.Info($"AuditTrailPage - SUCCESS: Returning view with {pagedItems?.Count() ?? 0} items on page {page}");
                return View(AuditTrailView.AuditTrailPage, searchVM);
            }
            catch (Exception ex)
            {
                logger.Error($"AuditTrailPage - CRITICAL ERROR: {ex.Message}");
                logger.Error($"AuditTrailPage - Stack Trace: {ex.StackTrace}");

                // Log additional context for debugging
                logger.Error($"AuditTrailPage - Error Context - actionid={actionid}, page={page}, Email='{Email}', PeriodFrom='{PeriodFrom}', PeriodTo='{PeriodTo}', ipAddress='{ipAddress}'");

                if (ex.InnerException != null)
                {
                    logger.Error($"AuditTrailPage - Inner Exception: {ex.InnerException.Message}");
                    logger.Error($"AuditTrailPage - Inner Stack Trace: {ex.InnerException.StackTrace}");
                }

                // Return error view or redirect to error page
                TempData["ErrorMessage"] = "An error occurred while processing your request. Please try again later.";
                return RedirectToAction("Error", "Home");
            }
        }

        // GET: Admin/AuditTrailListing
        [Route("AuditTrailListing/{page}", Name = AuditTrailControllerRoute.GetAuditTrailListing)]
        public ActionResult AuditTrailListing(int page, string module = "--All--", string Email = "", string PeriodFrom = "", string PeriodTo = "", string ipAddress = "")
        {
            logger.Info($"AuditTrailListing - START: page={page}, module='{module}', Email='{Email}', PeriodFrom='{PeriodFrom}', PeriodTo='{PeriodTo}', ipAddress='{ipAddress}'");

            try
            {
#if INTRANET
                if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                {
                    logger.Warn($"AuditTrailListing - OTP not verified, redirecting to OTP page");
                    return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
                }
#endif
                logger.Debug($"AuditTrailListing - Getting system parameters");
                int auditLogsPeriod = systemParametersService.GetAuditLogsStoragePeriod();
                int paginationSize = systemParametersService.GetPaginationPageSize();
                logger.Debug($"AuditTrailListing - System parameters: auditLogsPeriod={auditLogsPeriod}, paginationSize={paginationSize}");

                logger.Debug($"AuditTrailListing - Processing date parameters");
                DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
                if (!string.IsNullOrEmpty(PeriodFrom))
                {
                    try
                    {
                        tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                        logger.Debug($"AuditTrailListing - Parsed PeriodFrom: {tPeriodFrom}");
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"AuditTrailListing - Error parsing PeriodFrom '{PeriodFrom}': {ex.Message}");
                        throw new ArgumentException($"Invalid PeriodFrom date format: {PeriodFrom}", ex);
                    }
                }
                else
                {
                    PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
                    logger.Debug($"AuditTrailListing - Default PeriodFrom set to: {PeriodFrom}");
                }

                DateTimeOffset tPeriodTo = DateTimeOffset.Now;
                if (!string.IsNullOrEmpty(PeriodTo))
                {
                    try
                    {
                        tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                        logger.Debug($"AuditTrailListing - Parsed PeriodTo: {tPeriodTo}");
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"AuditTrailListing - Error parsing PeriodTo '{PeriodTo}': {ex.Message}");
                        throw new ArgumentException($"Invalid PeriodTo date format: {PeriodTo}", ex);
                    }
                }
                else
                {
                    PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
                    logger.Debug($"AuditTrailListing - Default PeriodTo set to: {PeriodTo}");
                }

                logger.Debug($"AuditTrailListing - Calling GetAuditTrails");
                IPagedList<AuditTrailLogging> pagedItems = auditTrailService.GetAuditTrails(module, Email, tPeriodFrom, tPeriodTo, ipAddress, page - 1, paginationSize);
                logger.Info($"AuditTrailListing - Retrieved {pagedItems?.Count() ?? 0} items, TotalItemCount={pagedItems?.TotalItemCount ?? 0}");

                ViewBag.Title = "Audit Log";
                ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Transactions;
                ConstructBreadcrumbs(new Breadcrumb() { Name = "Audit Log Listing", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailListing, new { actionid = (int)BIOME.Enumerations.Audit.Action.Transactions, page = 1 }) });

                if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                {
                    logger.Debug($"AuditTrailListing - Logging audit trail access");
                    auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_AuditLogListing, "");
                }

                ViewBag.pagedItems = pagedItems;
                ViewBag.varpage = page;
                ViewBag.actionid = 0;
#if (DEBUG || UAT)
                ViewBag.UAT = true;
#endif

                logger.Debug($"AuditTrailListing - Building module list");
                List<string> list = new List<string>();
                foreach (FieldInfo finfo in typeof(BIOME.Enumerations.Audit.Module).GetFields())
                {
                    list.Add(finfo.GetValue(null).ToString());
                }
                list.Sort();
                list.Insert(0, "--All--");
                ViewBag.moduleList = list;

                logger.Debug($"AuditTrailListing - Creating search view model");
                AuditTrailViewModel.SearchViewModel searchVM = new AuditTrailViewModel.SearchViewModel();
                searchVM.module = module;
                searchVM.Email = Email;
                searchVM.PeriodFrom = PeriodFrom;
                searchVM.PeriodTo = PeriodTo;

                logger.Info($"AuditTrailListing - SUCCESS: Returning view with {pagedItems?.Count() ?? 0} items on page {page}");
                return View(AuditTrailView.AuditTrail, searchVM);
            }
            catch (Exception ex)
            {
                logger.Error($"AuditTrailListing - CRITICAL ERROR: {ex.Message}");
                logger.Error($"AuditTrailListing - Stack Trace: {ex.StackTrace}");

                // Log additional context for debugging
                logger.Error($"AuditTrailListing - Error Context - page={page}, module='{module}', Email='{Email}', PeriodFrom='{PeriodFrom}', PeriodTo='{PeriodTo}', ipAddress='{ipAddress}'");

                if (ex.InnerException != null)
                {
                    logger.Error($"AuditTrailListing - Inner Exception: {ex.InnerException.Message}");
                    logger.Error($"AuditTrailListing - Inner Stack Trace: {ex.InnerException.StackTrace}");
                }

                // Return error view or redirect to error page
                TempData["ErrorMessage"] = "An error occurred while processing your request. Please try again later.";
                return RedirectToAction("Error", "Home");
            }
        }


        [AjaxAuthorization]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult SendAuditLogReport()
        {
            Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.SendAuditLogMonthlyReport(true));
            return Json(true);
        }

        // Post: Admin/MaintainableListPage
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("PostSearch/{actionid}/{page}", Name = AuditTrailControllerRoute.PostSearch)]
        public ActionResult PostSearch(int actionid, int page, AuditTrailViewModel.SearchViewModel searchVM)
        {
            return AuditTrailPage(actionid, 1, searchVM.Email, searchVM.PeriodFrom, searchVM.PeriodTo,searchVM.ipAddress);
        }

        // Post: Admin/AuditTrailListing
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("PostAuditTrailListing/{page}", Name = AuditTrailControllerRoute.PostAuditTrailListing)]
        public ActionResult PostAuditTrailListing(int page, AuditTrailViewModel.SearchViewModel searchVM)
        {
            page = 1;
            return AuditTrailListing(page, searchVM.module, searchVM.Email, searchVM.PeriodFrom, searchVM.PeriodTo, searchVM.ipAddress);
        }

        // GET: Admin/AuditTrailPage
        [Route("AuditLogPage/{actionid}/{page}", Name = AuditTrailControllerRoute.GetAuditLogPage)]
        public ActionResult AuditLogPage(  int page, string Email = "", string PeriodFrom = "", string PeriodTo = "", string ModuleName = "")
        {
            // Add detailed debug logging for audit log page access
            logger.Info($"AuditLogPage - START: page={page}, Email='{Email}', PeriodFrom='{PeriodFrom}', PeriodTo='{PeriodTo}', ModuleName='{ModuleName}'");

            try
            {
#if INTRANET
                if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                {
                    logger.Warn($"AuditLogPage - OTP not verified, redirecting to OTP page");
                    return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
                }
#endif
                logger.Debug($"AuditLogPage - Getting system parameters");
                int auditLogsPeriod = systemParametersService.GetAuditLogsStoragePeriod();
                int paginationSize = systemParametersService.GetPaginationPageSize();
                logger.Debug($"AuditLogPage - System parameters: auditLogsPeriod={auditLogsPeriod}, paginationSize={paginationSize}");

                List<AuditLog> auditTrailList = new List<AuditLog>();
                List<AuditTrailViewModel.AuditlogViewModel> viewauditTrailList = new List<AuditTrailViewModel.AuditlogViewModel>();
            auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_AuditDetails, "");

            //            if (!string.IsNullOrEmpty(Email) || !string.IsNullOrEmpty(PeriodFrom) || !string.IsNullOrEmpty(PeriodTo) || !string.IsNullOrEmpty(ModuleName))
            //            {
            //                DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
            //                if (!string.IsNullOrEmpty(PeriodFrom))
            //                {
            //                    tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            //                }

            //                DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            //                if (!string.IsNullOrEmpty(PeriodTo))
            //                {
            //                    tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            //                }




            //                auditTrailList = auditTrailService.GetListAuditLog(Email, tPeriodFrom, tPeriodTo, ModuleName);
            //                List<ApplicationUser> listUsers = userService.GetUserByIdList(auditTrailList.Select(o => (long)o.UserID).ToArray());
            //                viewauditTrailList = (from a in auditTrailList
            //                                      select new AuditTrailViewModel.AuditlogViewModel
            //                                      {
            //                                          ColumnName = a.ColumnName,
            //                                          EventDate = a.EventDate,
            //                                          EventType = getAuditEventType(a.EventType.ToLower()),
            //                                          NewValue = a.NewValue
            //,
            //                                          OriginalValue = a.OriginalValue,
            //                                          RecordID = a.RecordID,
            //                                          TableName = a.TableName,
            //                                          UserEmail = GetUserEmailByID(listUsers, a.UserID)
            //                                      }).ToList();
            //            }

                logger.Debug($"AuditLogPage - Processing date parameters");
                DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
                if (!string.IsNullOrEmpty(PeriodFrom))
                {
                    try
                    {
                        tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                        logger.Debug($"AuditLogPage - Parsed PeriodFrom: {tPeriodFrom}");
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"AuditLogPage - Error parsing PeriodFrom '{PeriodFrom}': {ex.Message}");
                        throw new ArgumentException($"Invalid PeriodFrom date format: {PeriodFrom}", ex);
                    }
                }
                else
                {
                    PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
                    logger.Debug($"AuditLogPage - Default PeriodFrom set to: {PeriodFrom}");
                }

                DateTimeOffset tPeriodTo = DateTimeOffset.Now;
                if (!string.IsNullOrEmpty(PeriodTo))
                {
                    try
                    {
                        tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                        logger.Debug($"AuditLogPage - Parsed PeriodTo: {tPeriodTo}");
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"AuditLogPage - Error parsing PeriodTo '{PeriodTo}': {ex.Message}");
                        throw new ArgumentException($"Invalid PeriodTo date format: {PeriodTo}", ex);
                    }
                }
                else
                {
                    PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
                    logger.Debug($"AuditLogPage - Default PeriodTo set to: {PeriodTo}");
                }




                logger.Debug($"AuditLogPage - Calling GetListAuditLog with Email='{Email}', ModuleName='{ModuleName}'");
                auditTrailList = auditTrailService.GetListAuditLog(Email, tPeriodFrom, tPeriodTo, ModuleName);
                logger.Info($"AuditLogPage - Retrieved {auditTrailList?.Count ?? 0} audit log records");

                if (auditTrailList == null)
                {
                    logger.Warn($"AuditLogPage - GetListAuditLog returned null, initializing empty list");
                    auditTrailList = new List<AuditLog>();
                }

                logger.Debug($"AuditLogPage - Getting user list for {auditTrailList.Count} audit records");
                var userIds = auditTrailList.Select(o => (long)o.UserID).ToArray();
                logger.Debug($"AuditLogPage - Unique user IDs to lookup: [{string.Join(", ", userIds.Distinct())}]");

                List<ApplicationUser> listUsers = userService.GetUserByIdList(userIds);
                logger.Info($"AuditLogPage - Retrieved {listUsers?.Count ?? 0} users for lookup");

                if (listUsers == null)
                {
                    logger.Warn($"AuditLogPage - GetUserByIdList returned null, initializing empty list");
                    listUsers = new List<ApplicationUser>();
                }

                logger.Debug($"AuditLogPage - Building view model list");
                viewauditTrailList = (from a in auditTrailList
                                      select new AuditTrailViewModel.AuditlogViewModel
                                      {
                                          ColumnName = a.ColumnName,
                                          EventDate = a.EventDate,
                                          EventType = getAuditEventType(a.EventType?.ToLower() ?? ""),
                                          NewValue = a.NewValue,
                                          OriginalValue = a.OriginalValue,
                                          RecordID = a.RecordID,
                                          TableName = a.TableName,
                                          UserEmail = GetUserEmailByID(listUsers, a.UserID)
                                      }).ToList();

                logger.Info($"AuditLogPage - Created {viewauditTrailList.Count} view model records");

                logger.Debug($"AuditLogPage - Creating paged list with page={page-1}, pageSize={paginationSize}");
                IPagedList <AuditTrailViewModel.AuditlogViewModel> pagedItems = viewauditTrailList.ToPagedList<AuditTrailViewModel.AuditlogViewModel>(page - 1, paginationSize);
                ViewBag.pagedItems = pagedItems;
                ViewBag.varpage = page;
          

                logger.Debug($"AuditLogPage - Creating search view model");
                AuditTrailViewModel.AuditlogSearchViewModel searchVM = new AuditTrailViewModel.AuditlogSearchViewModel();
                searchVM.Email = Email;
                searchVM.PeriodFrom = PeriodFrom;
                searchVM.PeriodTo = PeriodTo;
                searchVM.moduleName = ModuleName;

                logger.Info($"AuditLogPage - SUCCESS: Returning view with {pagedItems.Count()} items on page {page}");
                return View(AuditTrailView.AuditLogPage, searchVM);
            }
            catch (Exception ex)
            {
                logger.Error($"AuditLogPage - CRITICAL ERROR: {ex.Message}");
                logger.Error($"AuditLogPage - Stack Trace: {ex.StackTrace}");

                // Log additional context for debugging
                logger.Error($"AuditLogPage - Error Context - page={page}, Email='{Email}', PeriodFrom='{PeriodFrom}', PeriodTo='{PeriodTo}', ModuleName='{ModuleName}'");

                if (ex.InnerException != null)
                {
                    logger.Error($"AuditLogPage - Inner Exception: {ex.InnerException.Message}");
                    logger.Error($"AuditLogPage - Inner Stack Trace: {ex.InnerException.StackTrace}");
                }

                // Return error view or redirect to error page
                TempData["ErrorMessage"] = "An error occurred while processing your request. Please try again later.";
                return RedirectToAction("Error", "Home");
            }
        }
        private string getAuditEventType(string eventType)
        {
            if (eventType == "a")
                return "ADD";
            else if (eventType == "m")
                return "EDIT";
            else if (eventType == "d")
                return "DELETE";
            else if (eventType == "l")
                return "CHECK LOCK";

            return "";
        }

        // Post: Admin/MaintainableListPage
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("AuditlogPostSearch/{actionid}/{page}", Name = AuditTrailControllerRoute.AuditlogPostSearch)]
        public ActionResult AuditLogPostSearch(  int page, AuditTrailViewModel.AuditlogSearchViewModel searchVM)
        {
            return AuditLogPage(  1, searchVM.Email, searchVM.PeriodFrom, searchVM.PeriodTo, searchVM.moduleName);
        }


        public string GetUserEmailByID(int id)
        {
           
          return   userService.GetUserById(id)?.Email;
        }
        private string GetUserEmailByID(List<ApplicationUser> listUsers, int id)
        {
            try
            {
                logger.Debug($"GetUserEmailByID - Looking up user ID: {id}");

                if (listUsers == null)
                {
                    logger.Warn($"GetUserEmailByID - listUsers is null for ID: {id}");
                    return "Unknown User (List Null)";
                }

                ApplicationUser user = listUsers.Where(o => o.Id == id).SingleOrDefault();

                if (user == null)
                {
                    logger.Warn($"GetUserEmailByID - User not found for ID: {id}");
                    return "Unknown User (Not Found)";
                }

                if (string.IsNullOrEmpty(user.Email))
                {
                    logger.Warn($"GetUserEmailByID - User found but email is null/empty for ID: {id}");
                    return "Unknown Email";
                }

                logger.Debug($"GetUserEmailByID - Found email '{user.Email}' for ID: {id}");
                return user.Email;
            }
            catch (Exception ex)
            {
                logger.Error($"GetUserEmailByID - Error looking up user ID {id}: {ex.Message}");
                return "Error Getting Email";
            }
        }


        #endregion
    }
}